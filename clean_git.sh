#!/bin/bash

# 脚本功能：删除项目中的所有Git相关文件
# 使用方法：将此脚本放在项目根目录下执行

# 确认是否继续
echo "警告：此操作将永久删除所有Git版本控制信息，无法恢复！"
read -p "确定要继续吗？(y/n): " confirm

if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "操作已取消"
    exit 0
fi

# 删除.git目录
if [ -d ".git" ]; then
    echo "删除.git目录..."
    rm -rf .git
fi

# 删除其他可能的Git相关文件
echo "删除其他Git相关文件..."
find -L . -name ".gitignore" -type f -delete
find -L . -name ".gitmodules" -type f -delete
find -L . -name ".gitattributes" -type f -delete
find -L . -name ".git-*" -type f -delete
find -L . -name ".gitkeep" -type f -delete

echo "完成！所有Git相关文件已删除。"