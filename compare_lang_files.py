#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较两个lang文件中JSON key的差异
"""

import json
import sys
from typing import Dict, Set, Tuple

def load_json_file(file_path: str) -> Dict:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误：文件 {file_path} 不是有效的JSON格式: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误：读取文件 {file_path} 时发生错误: {e}")
        sys.exit(1)

def compare_json_keys(file1_data: Dict, file2_data: Dict, file1_name: str, file2_name: str) -> <PERSON><PERSON>[Set, Set, Set]:
    """比较两个JSON文件的key"""
    keys1 = set(file1_data.keys())
    keys2 = set(file2_data.keys())
    
    # 只在文件1中存在的key
    only_in_file1 = keys1 - keys2
    # 只在文件2中存在的key
    only_in_file2 = keys2 - keys1
    # 两个文件都有的key
    common_keys = keys1 & keys2
    
    return only_in_file1, only_in_file2, common_keys

def print_comparison_results(only_in_file1: Set, only_in_file2: Set, common_keys: Set, 
                           file1_name: str, file2_name: str, file1_data: Dict, file2_data: Dict):
    """打印比较结果"""
    print("=" * 80)
    print(f"JSON Key 比较结果")
    print("=" * 80)
    print(f"文件1: {file1_name}")
    print(f"文件2: {file2_name}")
    print()
    
    print(f"📊 统计信息:")
    print(f"  文件1总key数: {len(file1_data)}")
    print(f"  文件2总key数: {len(file2_data)}")
    print(f"  共同key数: {len(common_keys)}")
    print(f"  仅在文件1中的key数: {len(only_in_file1)}")
    print(f"  仅在文件2中的key数: {len(only_in_file2)}")
    print()
    
    if only_in_file1:
        print(f"🔴 仅在 {file1_name} 中存在的key ({len(only_in_file1)}个):")
        print("-" * 60)
        for key in sorted(only_in_file1):
            value = file1_data[key]
            print(f"  {key}: \"{value}\"")
        print()
    
    if only_in_file2:
        print(f"🔵 仅在 {file2_name} 中存在的key ({len(only_in_file2)}个):")
        print("-" * 60)
        for key in sorted(only_in_file2):
            value = file2_data[key]
            print(f"  {key}: \"{value}\"")
        print()
    
    if not only_in_file1 and not only_in_file2:
        print("✅ 两个文件的key完全一致！")
    
    # 检查相同key但值不同的情况
    different_values = []
    for key in common_keys:
        if file1_data[key] != file2_data[key]:
            different_values.append(key)
    
    if different_values:
        print(f"⚠️  相同key但值不同的项 ({len(different_values)}个):")
        print("-" * 60)
        for key in sorted(different_values):
            print(f"  {key}:")
            print(f"    {file1_name}: \"{file1_data[key]}\"")
            print(f"    {file2_name}: \"{file2_data[key]}\"")
            print()

def main():
    """主函数"""
    # 文件路径
    file1_path = "appsdk/Resource/language/zh/lang"
    file2_path = "lang"
    
    print("正在比较两个lang文件...")
    print(f"文件1: {file1_path}")
    print(f"文件2: {file2_path}")
    print()
    
    # 加载JSON文件
    file1_data = load_json_file(file1_path)
    file2_data = load_json_file(file2_path)
    
    # 比较key
    only_in_file1, only_in_file2, common_keys = compare_json_keys(
        file1_data, file2_data, file1_path, file2_path
    )
    
    # 打印结果
    print_comparison_results(
        only_in_file1, only_in_file2, common_keys,
        file1_path, file2_path, file1_data, file2_data
    )

if __name__ == "__main__":
    main()
